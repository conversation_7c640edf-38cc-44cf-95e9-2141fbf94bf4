// Marcus Lion Transcribe - Frontend JavaScript

class TranscribeApp {
    constructor() {
        this.token = localStorage.getItem('token');
        this.currentUser = null;
        this.currentPage = 1;
        this.perPage = 10;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
        this.handleTokenFromURL();
    }

    setupEventListeners() {
        // Auth form toggles
        document.getElementById('show-register')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.showRegisterForm();
        });

        document.getElementById('show-login')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.showLoginForm();
        });

        // Auth buttons
        document.getElementById('login-btn')?.addEventListener('click', () => {
            this.showLoginForm();
        });

        document.getElementById('register-btn')?.addEventListener('click', () => {
            this.showRegisterForm();
        });

        document.getElementById('logout-btn')?.addEventListener('click', () => {
            this.logout();
        });

        // Form submissions
        document.getElementById('login-form-element')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('register-form-element')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // File upload
        document.getElementById('browse-btn')?.addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input')?.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0]);
        });

        // Drag and drop
        const dropZone = document.getElementById('drop-zone');
        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleFileSelect(files[0]);
                }
            });
        }

        // API key management
        document.getElementById('generate-api-key')?.addEventListener('click', () => {
            this.generateApiKey();
        });

        document.getElementById('toggle-api-key')?.addEventListener('click', () => {
            this.toggleApiKeyVisibility();
        });

        document.getElementById('copy-api-key')?.addEventListener('click', () => {
            this.copyApiKey();
        });

        // Pagination
        document.getElementById('prev-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadTranscriptions();
            }
        });

        document.getElementById('next-page')?.addEventListener('click', () => {
            this.currentPage++;
            this.loadTranscriptions();
        });

        // Modal
        document.querySelector('.close')?.addEventListener('click', () => {
            this.closeModal();
        });

        window.addEventListener('click', (e) => {
            const modal = document.getElementById('transcription-modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    handleTokenFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        if (token) {
            this.token = token;
            localStorage.setItem('token', token);
            window.history.replaceState({}, document.title, window.location.pathname);
            this.checkAuthStatus();
        }
    }

    async checkAuthStatus() {
        if (this.token) {
            try {
                const response = await this.apiCall('/auth/me', 'GET');
                this.currentUser = response;
                this.showDashboard();
            } catch (error) {
                this.logout();
            }
        } else {
            this.showAuthForms();
        }
    }

    showAuthForms() {
        document.getElementById('auth-forms').style.display = 'block';
        document.getElementById('dashboard').style.display = 'none';
        document.getElementById('auth-section').style.display = 'block';
        document.getElementById('user-section').style.display = 'none';
    }

    showDashboard() {
        document.getElementById('auth-forms').style.display = 'none';
        document.getElementById('dashboard').style.display = 'block';
        document.getElementById('auth-section').style.display = 'none';
        document.getElementById('user-section').style.display = 'block';

        if (this.currentUser) {
            document.getElementById('user-email').textContent = this.currentUser.email;
            document.getElementById('api-key-display').value = this.currentUser.api_key || '';
        }

        this.loadTranscriptions();
    }

    showLoginForm() {
        document.getElementById('login-form').style.display = 'block';
        document.getElementById('register-form').style.display = 'none';
    }

    showRegisterForm() {
        document.getElementById('login-form').style.display = 'none';
        document.getElementById('register-form').style.display = 'block';
    }

    async handleLogin() {
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        try {
            const formData = new FormData();
            formData.append('username', email);
            formData.append('password', password);

            const response = await fetch('/auth/login', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('token', this.token);
                this.checkAuthStatus();
                this.showToast('Login successful!', 'success');
            } else {
                const error = await response.json();
                this.showToast(error.detail || 'Login failed', 'error');
            }
        } catch (error) {
            this.showToast('Login failed', 'error');
        }
    }

    async handleRegister() {
        const email = document.getElementById('register-email').value;
        const fullName = document.getElementById('register-name').value;
        const password = document.getElementById('register-password').value;

        if (!email || !password) {
            this.showToast('Email and password are required', 'error');
            return;
        }

        try {
            const response = await this.apiCall('/auth/register', 'POST', {
                email,
                full_name: fullName || null,
                password
            });

            this.showToast('Registration successful! You are now logged in.', 'success');

            // Auto-login after successful registration in dev mode
            if (response.id === 'dev-user-123') {
                // Create a token for the dev user using form data
                const formData = new FormData();
                formData.append('username', email);
                formData.append('password', password);

                const loginResponse = await fetch('/auth/login', {
                    method: 'POST',
                    body: formData
                });

                if (loginResponse.ok) {
                    const data = await loginResponse.json();
                    this.token = data.access_token;
                    localStorage.setItem('token', this.token);
                    this.checkAuthStatus();
                }
            } else {
                this.showLoginForm();
            }
        } catch (error) {
            this.showToast(error.message || 'Registration failed', 'error');
        }
    }

    logout() {
        this.token = null;
        this.currentUser = null;
        localStorage.removeItem('token');
        this.showAuthForms();
        this.showToast('Logged out successfully', 'success');
    }

    async handleFileSelect(file) {
        if (!file) return;

        const language = document.getElementById('language-select').value;
        const formData = new FormData();
        formData.append('file', file);
        formData.append('language', language);

        this.showProgress('Uploading file...');

        try {
            const response = await fetch('/transcriptions/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                },
                body: formData
            });

            if (response.ok) {
                const transcription = await response.json();
                this.hideProgress();
                this.showToast('File uploaded successfully! Transcription in progress...', 'success');
                this.loadTranscriptions();

                // Poll for completion
                this.pollTranscriptionStatus(transcription.id);
            } else {
                const error = await response.json();
                this.hideProgress();
                this.showToast(error.detail || 'Upload failed', 'error');
            }
        } catch (error) {
            this.hideProgress();
            this.showToast('Upload failed', 'error');
        }
    }

    async pollTranscriptionStatus(transcriptionId) {
        const maxAttempts = 30; // 5 minutes max
        let attempts = 0;

        const poll = async () => {
            try {
                const transcription = await this.apiCall(`/transcriptions/${transcriptionId}`, 'GET');

                if (transcription.status === 'completed' || transcription.status === 'failed') {
                    this.loadTranscriptions();
                    if (transcription.status === 'completed') {
                        this.showToast('Transcription completed!', 'success');
                    } else {
                        this.showToast('Transcription failed', 'error');
                    }
                    return;
                }

                attempts++;
                if (attempts < maxAttempts) {
                    setTimeout(poll, 10000); // Poll every 10 seconds
                }
            } catch (error) {
                console.error('Polling error:', error);
            }
        };

        poll();
    }

    async loadTranscriptions() {
        try {
            const skip = (this.currentPage - 1) * this.perPage;
            const response = await this.apiCall(`/transcriptions/?skip=${skip}&limit=${this.perPage}`, 'GET');

            this.renderTranscriptions(response.transcriptions);
            this.updatePagination(response);
        } catch (error) {
            this.showToast('Failed to load transcriptions', 'error');
        }
    }

    renderTranscriptions(transcriptions) {
        const container = document.getElementById('transcriptions-list');
        if (!container) return;

        if (transcriptions.length === 0) {
            container.innerHTML = '<p>No transcriptions yet. Upload an audio file to get started!</p>';
            return;
        }

        container.innerHTML = transcriptions.map(t => `
            <div class="transcription-item" onclick="app.showTranscriptionDetails('${t.id}')">
                <div class="transcription-header">
                    <div class="transcription-title">${t.original_filename}</div>
                    <div class="transcription-status status-${t.status}">${t.status}</div>
                </div>
                <div class="transcription-meta">
                    <span><i class="fas fa-calendar"></i> ${new Date(t.created_at).toLocaleDateString()}</span>
                    <span><i class="fas fa-file"></i> ${(t.file_size / 1024 / 1024).toFixed(2)} MB</span>
                    ${t.processing_time ? `<span><i class="fas fa-clock"></i> ${t.processing_time.toFixed(1)}s</span>` : ''}
                </div>
                ${t.transcription_text ? `<div class="transcription-preview">${t.transcription_text.substring(0, 150)}...</div>` : ''}
            </div>
        `).join('');
    }

    updatePagination(response) {
        const pageInfo = document.getElementById('page-info');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');

        if (pageInfo) {
            pageInfo.textContent = `Page ${this.currentPage}`;
        }

        if (prevBtn) {
            prevBtn.disabled = this.currentPage <= 1;
        }

        if (nextBtn) {
            nextBtn.disabled = response.transcriptions.length < this.perPage;
        }
    }

    async showTranscriptionDetails(transcriptionId) {
        try {
            const transcription = await this.apiCall(`/transcriptions/${transcriptionId}`, 'GET');

            const modalContent = document.getElementById('modal-content');
            modalContent.innerHTML = `
                <h3>${transcription.original_filename}</h3>
                <div class="transcription-details">
                    <p><strong>Status:</strong> <span class="transcription-status status-${transcription.status}">${transcription.status}</span></p>
                    <p><strong>File Size:</strong> ${(transcription.file_size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>Language:</strong> ${transcription.language}</p>
                    <p><strong>Created:</strong> ${new Date(transcription.created_at).toLocaleString()}</p>
                    ${transcription.processing_time ? `<p><strong>Processing Time:</strong> ${transcription.processing_time.toFixed(1)} seconds</p>` : ''}
                    ${transcription.confidence_score ? `<p><strong>Confidence:</strong> ${(transcription.confidence_score * 100).toFixed(1)}%</p>` : ''}
                </div>
                ${transcription.transcription_text ? `
                    <div class="transcription-text">
                        <h4>Transcription:</h4>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">${transcription.transcription_text}</div>
                        <button class="btn btn-secondary" onclick="app.copyTranscription('${transcription.transcription_text.replace(/'/g, "\\'")}')">
                            <i class="fas fa-copy"></i> Copy Text
                        </button>
                    </div>
                ` : ''}
                ${transcription.error_message ? `
                    <div class="error-message">
                        <h4>Error:</h4>
                        <p style="color: #dc3545;">${transcription.error_message}</p>
                        <button class="btn btn-primary" onclick="app.reprocessTranscription('${transcription.id}')">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </div>
                ` : ''}
                <div style="margin-top: 2rem;">
                    <button class="btn btn-secondary" onclick="app.deleteTranscription('${transcription.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            `;

            document.getElementById('transcription-modal').style.display = 'block';
        } catch (error) {
            this.showToast('Failed to load transcription details', 'error');
        }
    }

    closeModal() {
        document.getElementById('transcription-modal').style.display = 'none';
    }

    copyTranscription(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('Transcription copied to clipboard!', 'success');
        });
    }

    async reprocessTranscription(transcriptionId) {
        try {
            await this.apiCall(`/transcriptions/${transcriptionId}/reprocess`, 'POST');
            this.showToast('Reprocessing started...', 'success');
            this.closeModal();
            this.loadTranscriptions();
            this.pollTranscriptionStatus(transcriptionId);
        } catch (error) {
            this.showToast('Failed to reprocess transcription', 'error');
        }
    }

    async deleteTranscription(transcriptionId) {
        if (!confirm('Are you sure you want to delete this transcription?')) return;

        try {
            await this.apiCall(`/transcriptions/${transcriptionId}`, 'DELETE');
            this.showToast('Transcription deleted', 'success');
            this.closeModal();
            this.loadTranscriptions();
        } catch (error) {
            this.showToast('Failed to delete transcription', 'error');
        }
    }

    async generateApiKey() {
        try {
            const response = await this.apiCall('/auth/api-key', 'POST');
            document.getElementById('api-key-display').value = response.api_key;
            this.showToast('New API key generated!', 'success');
        } catch (error) {
            this.showToast('Failed to generate API key', 'error');
        }
    }

    toggleApiKeyVisibility() {
        const input = document.getElementById('api-key-display');
        const button = document.getElementById('toggle-api-key');

        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    copyApiKey() {
        const input = document.getElementById('api-key-display');
        navigator.clipboard.writeText(input.value).then(() => {
            this.showToast('API key copied to clipboard!', 'success');
        });
    }

    showProgress(message) {
        const progressSection = document.getElementById('upload-progress');
        const progressText = document.getElementById('progress-text');

        if (progressSection && progressText) {
            progressText.textContent = message;
            progressSection.style.display = 'block';
        }
    }

    hideProgress() {
        const progressSection = document.getElementById('upload-progress');
        if (progressSection) {
            progressSection.style.display = 'none';
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div>${message}</div>
        `;

        container.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (this.token) {
            options.headers['Authorization'] = `Bearer ${this.token}`;
        }

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(endpoint, options);

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'API call failed');
        }

        return await response.json();
    }
}

// Initialize the app
const app = new TranscribeApp();
