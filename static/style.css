/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 18px; /* Increased base font size */
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.8rem; /* Increased font size */
    font-weight: bold;
    gap: 0.75rem;
}

.nav-brand .logo {
    height: 50px;
    width: auto;
    max-width: 50px;
    object-fit: contain;
    border-radius: 8px;
}

.nav-brand .logo-fallback {
    display: none;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

.nav-brand .logo-fallback i {
    font-size: 1.5rem;
    color: white;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem; /* Increased padding */
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem; /* Increased font size */
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-google {
    background: #db4437;
    color: white;
}

.btn-github {
    background: #333;
    color: white;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Main content */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

/* Setup Status */
.setup-status {
    margin-bottom: 3rem;
}

.status-card {
    background: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-left: 5px solid #667eea;
}

.status-card h2 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-card h2 i {
    color: #667eea;
}

.setup-list {
    list-style: none;
    margin: 2rem 0;
}

.setup-list li {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #e9ecef;
}

.setup-list li i {
    color: #667eea;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.setup-list li strong {
    font-size: 1.2rem;
    color: #333;
}

.setup-list li p {
    margin: 0.5rem 0 0 2.5rem;
    color: #666;
    font-size: 1rem;
}

.status-badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-left: 1rem;
}

.status-connected {
    background: #d4edda;
    color: #155724;
}

.status-disconnected {
    background: #f8d7da;
    color: #721c24;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-optional {
    background: #cce5ff;
    color: #004085;
}

.setup-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.setup-list code {
    background: #e9ecef;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.dev-mode-notice {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.dev-mode-notice h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dev-mode-notice p {
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.dev-mode-notice code {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

/* Auth forms */
.auth-container {
    max-width: 500px;
    margin: 4rem auto;
}

.auth-form {
    background: white;
    padding: 3rem; /* Increased padding */
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-size: 2rem; /* Increased font size */
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
    font-size: 1.1rem; /* Increased font size */
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem; /* Increased padding */
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem; /* Increased font size */
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.oauth-section {
    margin: 2rem 0;
    text-align: center;
}

.oauth-section p {
    margin-bottom: 1rem;
    color: #666;
    font-size: 1rem; /* Increased font size */
}

.oauth-buttons {
    display: flex;
    gap: 1rem;
}

.auth-switch {
    text-align: center;
    margin-top: 2rem;
    color: #666;
    font-size: 1rem; /* Increased font size */
}

.auth-switch a {
    color: #667eea;
    text-decoration: none;
}

/* Dashboard */
.upload-section,
.transcriptions-section,
.api-section {
    background: white;
    padding: 2.5rem; /* Increased padding */
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.upload-section h2,
.transcriptions-section h2,
.api-section h2 {
    margin-bottom: 2rem;
    color: #333;
    font-size: 1.8rem; /* Increased font size */
}

/* Drop zone */
.drop-zone {
    border: 3px dashed #ddd;
    border-radius: 12px;
    padding: 3rem; /* Increased padding */
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drop-zone:hover,
.drop-zone.dragover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.drop-zone i {
    font-size: 3rem; /* Increased icon size */
    color: #667eea;
    margin-bottom: 1rem;
}

.drop-zone p {
    margin-bottom: 1.5rem;
    color: #666;
    font-size: 1.2rem; /* Increased font size */
}

/* Upload options */
.upload-options {
    margin-top: 2rem;
    max-width: 300px;
}

/* Progress */
.progress-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 12px; /* Increased height */
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Transcriptions list */
.transcriptions-list {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.transcription-item {
    background: #f8f9fa;
    padding: 2rem; /* Increased padding */
    border-radius: 8px;
    border-left: 4px solid #667eea;
    cursor: pointer;
    transition: all 0.3s ease;
}

.transcription-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.transcription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.transcription-title {
    font-weight: 600;
    color: #333;
    font-size: 1.2rem; /* Increased font size */
}

.transcription-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem; /* Increased font size */
    font-weight: 500;
}

.status-completed { background: #d4edda; color: #155724; }
.status-processing { background: #fff3cd; color: #856404; }
.status-pending { background: #cce5ff; color: #004085; }
.status-failed { background: #f8d7da; color: #721c24; }

.transcription-meta {
    display: flex;
    gap: 2rem;
    color: #666;
    font-size: 0.95rem; /* Increased font size */
    margin-bottom: 1rem;
}

.transcription-preview {
    color: #555;
    font-style: italic;
    font-size: 1rem; /* Increased font size */
    max-height: 3em;
    overflow: hidden;
}

/* API Key section */
.api-key-container {
    max-width: 600px;
}

.api-key-display {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.api-key-display input {
    flex: 1;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2.5rem; /* Increased padding */
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close {
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    font-size: 2rem; /* Increased font size */
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1001;
}

.toast {
    background: white;
    padding: 1.5rem; /* Increased padding */
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
    min-width: 300px;
    animation: slideIn 0.3s ease;
    font-size: 1rem; /* Increased font size */
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.success {
    border-left-color: #28a745;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

#page-info {
    font-size: 1.1rem; /* Increased font size */
    color: #666;
}

/* Landing Page Styles */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.feature-item i {
    color: #ffd700;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.2rem;
}

.global-languages {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    margin-bottom: 2rem;
}

.global-languages h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.flags-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.flag-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease;
}

.flag-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
}

.flag {
    font-size: 2rem;
}

.lang {
    font-size: 0.9rem;
    text-align: center;
}

.auto-detect {
    text-align: center;
    font-style: italic;
    opacity: 0.8;
    margin-top: 1rem;
}

.audio-wave {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: 4px;
    height: 60px;
}

.wave-bar {
    width: 8px;
    background: linear-gradient(to top, #ffd700, #fff);
    border-radius: 4px;
    animation: wave 1.5s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { height: 20px; }
    50% { height: 60px; }
}

/* Newscaster Scene Styles */
.workflow-demo {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.demo-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.newscaster-scene {
    position: relative;
    width: 300px;
    height: 200px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.news-studio {
    position: relative;
    width: 100%;
    height: 100%;
}

.background-screen {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 80px;
    height: 50px;
    background: #000;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.news-logo {
    color: #ff0000;
    font-size: 0.8rem;
    font-weight: bold;
}

.news-desk {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to bottom, #8B4513, #654321);
    border-radius: 8px 8px 0 0;
}

.newscaster {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: end;
    gap: 10px;
}

.person {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.head {
    position: relative;
    width: 40px;
    height: 40px;
    background: #FDBCB4;
    border-radius: 50%;
    margin-bottom: 5px;
}

.hair {
    position: absolute;
    top: -5px;
    left: 5px;
    right: 5px;
    height: 20px;
    background: #8B4513;
    border-radius: 20px 20px 0 0;
}

.face {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.eyes {
    display: flex;
    gap: 8px;
    margin-bottom: 5px;
}

.eye {
    width: 4px;
    height: 4px;
    background: #000;
    border-radius: 50%;
}

.mouth {
    width: 8px;
    height: 4px;
    background: #000;
    border-radius: 0 0 8px 8px;
    margin: 0 auto;
}

.mouth.speaking {
    animation: speak 1.5s ease-in-out infinite;
}

@keyframes speak {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(1.5); }
}

.body {
    position: relative;
    width: 35px;
    height: 45px;
    background: #000;
    border-radius: 8px 8px 0 0;
}

.suit {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 35px;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
}

.tie {
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 20px;
    background: #ff0000;
    clip-path: polygon(0 0, 100% 0, 80% 100%, 20% 100%);
}

.microphone {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.mic-head {
    width: 12px;
    height: 16px;
    background: #333;
    border-radius: 6px 6px 0 0;
    position: relative;
}

.mic-head::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    height: 8px;
    background: #666;
    border-radius: 4px;
}

.mic-stand {
    width: 2px;
    height: 30px;
    background: #333;
}

.speech-bubble {
    position: absolute;
    top: -60px;
    left: 20px;
    background: white;
    padding: 10px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    max-width: 200px;
    font-size: 0.8rem;
    color: #333;
}

.speech-bubble::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid white;
}

.sound-waves {
    display: flex;
    gap: 2px;
    margin-top: 5px;
    justify-content: center;
}

.sound-waves .wave {
    width: 3px;
    height: 8px;
    background: #667eea;
    border-radius: 2px;
    animation: soundWave 1s ease-in-out infinite;
}

.sound-waves .wave:nth-child(2) {
    animation-delay: 0.2s;
}

.sound-waves .wave:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes soundWave {
    0%, 100% { transform: scaleY(0.5); }
    50% { transform: scaleY(1.5); }
}

.arrow-down {
    font-size: 2rem;
    color: #667eea;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
}

.transcription-output {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.document {
    width: 300px;
}

.doc-header {
    background: #f8f9fa;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.doc-header i {
    color: #667eea;
}

.doc-content {
    padding: 1.5rem;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
}

.text-line {
    margin-bottom: 0.5rem;
    opacity: 0;
    animation: typeIn 3s ease-in-out infinite;
}

.text-line:nth-child(1) {
    animation-delay: 0s;
}

.text-line:nth-child(2) {
    animation-delay: 1s;
}

.text-line:nth-child(3) {
    animation-delay: 2s;
}

@keyframes typeIn {
    0%, 20% { opacity: 0; transform: translateX(-10px); }
    30%, 90% { opacity: 1; transform: translateX(0); }
    100% { opacity: 1; transform: translateX(0); }
}

.cursor {
    display: inline-block;
    animation: blink 1s ease-in-out infinite;
    font-weight: bold;
    color: #667eea;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.features {
    padding: 4rem 0;
    background: #f8f9fa;
}

.features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.features h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.how-it-works {
    padding: 4rem 0;
    background: white;
}

.how-it-works-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.how-it-works h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.step h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.status-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

.status-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.status-list {
    list-style: none;
    margin: 1.5rem 0;
}

.status-list li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
}

.status-list i {
    color: #667eea;
}

.cta {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand p {
    margin-top: 1rem;
    opacity: 0.8;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #667eea;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 1rem;
    text-align: center;
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .main-content {
        padding: 0 1rem;
    }

    .auth-form {
        padding: 2rem;
        margin: 2rem 1rem;
    }

    .oauth-buttons {
        flex-direction: column;
    }

    .transcription-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .transcription-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .api-key-display {
        flex-direction: column;
    }

    .nav-brand {
        font-size: 1.5rem;
    }

    .nav-brand .logo {
        height: 40px;
        max-width: 40px;
    }

    .nav-brand .logo-fallback {
        width: 40px;
        height: 40px;
    }
}
