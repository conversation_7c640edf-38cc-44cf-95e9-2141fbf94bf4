from motor.motor_asyncio import AsyncIOMotorClient
from config import settings
import logging

logger = logging.getLogger(__name__)

class Database:
    client: AsyncIOMotorClient = None
    database = None

db = Database()

async def get_database():
    if db.database is None:
        from fastapi import HTTPException
        raise HTTPException(status_code=503, detail="Database not available. Please configure MongoDB connection.")
    return db.database

async def connect_to_mongo():
    """Create database connection"""
    try:
        if not settings.mongodb_url or settings.mongodb_url == "mongodb://localhost:27017/transcriber":
            logger.warning("MongoDB URL not configured or using localhost. Running without database.")
            return

        db.client = AsyncIOMotorClient(settings.mongodb_url)
        db.database = db.client[settings.database_name]

        # Test the connection
        await db.client.admin.command('ping')
        logger.info("Successfully connected to MongoDB Atlas")

        # Create indexes
        await create_indexes()

    except Exception as e:
        logger.warning(f"Could not connect to MongoDB: {e}. Running without database.")
        db.client = None
        db.database = None

async def close_mongo_connection():
    """Close database connection"""
    if db.client:
        db.client.close()
        logger.info("Disconnected from MongoDB")

async def create_indexes():
    """Create database indexes for better performance"""
    if db.database is None:
        logger.warning("Database not available, skipping index creation")
        return

    try:
        # Users collection indexes
        await db.database.users.create_index("email", unique=True)
        await db.database.users.create_index("api_key", unique=True, sparse=True)

        # Transcriptions collection indexes
        await db.database.transcriptions.create_index("user_id")
        await db.database.transcriptions.create_index("created_at")
        await db.database.transcriptions.create_index("status")

        logger.info("Database indexes created successfully")
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")

# Dependency to get database instance
async def get_db():
    return await get_database()
