# Marcus Lion Transcribe

A professional audio transcription service built with FastAPI, featuring authentication, MongoDB storage, and a modern web interface.

## Features

- **Audio Transcription**: Upload MP3, WAV, M4A, and FLAC files for automatic transcription using OpenAI Whisper
- **Authentication**:
  - Email/password registration and login
  - OAuth integration with Google and GitHub
  - API key authentication for programmatic access
- **Database**: MongoDB Atlas integration for user data and transcription storage
- **Web Interface**: Modern, responsive UI with drag-and-drop file upload
- **API**: RESTful API with automatic documentation
- **Real-time Updates**: Background processing with status polling

## Tech Stack

- **Backend**: FastAPI, Python 3.8+
- **Database**: MongoDB Atlas
- **Authentication**: JWT tokens, OAuth2, API keys
- **Transcription**: OpenAI Whisper API
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Deployment**: Uvicorn ASGI server

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd transcriber
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Database
MONGODB_URL=mongodb+srv://username:<EMAIL>/transcriber?retryWrites=true&w=majority
DATABASE_NAME=transcriber

# JWT Settings
SECRET_KEY=your-super-secret-jwt-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OAuth Settings (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# OpenAI API (required for transcription)
OPENAI_API_KEY=your-openai-api-key

# App Settings
APP_NAME=Marcus Lion Transcribe
DEBUG=True
```

### 4. MongoDB Atlas Setup

1. Create a MongoDB Atlas account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Create a database user with read/write permissions
4. Get your connection string and add it to the `MONGODB_URL` in your `.env` file
5. Whitelist your IP address or use 0.0.0.0/0 for development

### 5. OpenAI API Setup

1. Create an OpenAI account at https://platform.openai.com
2. Generate an API key
3. Add the API key to `OPENAI_API_KEY` in your `.env` file

### 6. OAuth Setup (Optional)

#### Google OAuth:
1. Go to Google Cloud Console
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:8000/auth/google/callback`

#### GitHub OAuth:
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL: `http://localhost:8000/auth/github/callback`

### 7. Add Marcus Lion Logo

Place your Marcus Lion logo file as `static/marcus-lion-logo.png`. The app will automatically use it in the navigation. If no logo is found, it will fall back to a microphone icon.

### 8. Run the Application

```bash
python main.py
```

The application will be available at:
- Web Interface: http://localhost:8200
- API Documentation: http://localhost:8200/docs
- Alternative API Docs: http://localhost:8200/redoc

## Usage

### Web Interface

1. **Registration/Login**: Create an account or login with email/password or OAuth
2. **Upload Files**: Drag and drop audio files or click to browse
3. **Monitor Progress**: Watch real-time transcription status
4. **View Results**: Click on transcriptions to view full text and details
5. **API Access**: Generate API keys for programmatic access

### API Usage

#### Authentication
```bash
# Login to get access token
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=your-email&password=your-password"

# Or use API key in header
curl -H "X-API-Key: your-api-key" "http://localhost:8000/transcriptions/"
```

#### Upload and Transcribe
```bash
curl -X POST "http://localhost:8000/transcriptions/upload" \
  -H "Authorization: Bearer your-access-token" \
  -F "file=@audio.mp3" \
  -F "language=auto"
```

#### Get Transcriptions
```bash
curl -H "Authorization: Bearer your-access-token" \
  "http://localhost:8000/transcriptions/"
```

## File Structure

```
transcriber/
├── main.py                 # FastAPI application entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── database/
│   ├── __init__.py
│   └── connection.py     # MongoDB connection
├── models/
│   ├── __init__.py
│   ├── user.py          # User data models
│   └── transcription.py # Transcription data models
├── services/
│   ├── __init__.py
│   ├── auth_service.py  # Authentication logic
│   └── transcription_service.py # Transcription logic
├── routers/
│   ├── __init__.py
│   ├── auth.py          # Authentication routes
│   └── transcription.py # Transcription routes
├── middleware/
│   ├── __init__.py
│   └── auth.py          # Authentication middleware
├── templates/
│   └── index.html       # Main web interface
├── static/
│   ├── style.css        # Styling
│   ├── app.js          # Frontend JavaScript
│   └── marcus-lion-logo.png # Your logo (add this)
└── uploads/             # Uploaded files (created automatically)
```

## API Endpoints

- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `GET /auth/me` - Get current user info
- `POST /auth/api-key` - Generate API key
- `GET /auth/google` - Google OAuth login
- `GET /auth/github` - GitHub OAuth login
- `POST /transcriptions/upload` - Upload audio file
- `GET /transcriptions/` - List user transcriptions
- `GET /transcriptions/{id}` - Get specific transcription
- `DELETE /transcriptions/{id}` - Delete transcription
- `POST /transcriptions/{id}/reprocess` - Retry failed transcription

## Security Features

- JWT token authentication
- API key authentication
- Password hashing with bcrypt
- CORS protection
- File type validation
- File size limits
- User data isolation

## Deployment

For production deployment:

1. Set `DEBUG=False` in environment
2. Use a strong `SECRET_KEY`
3. Configure proper CORS origins
4. Use HTTPS
5. Set up proper MongoDB security
6. Consider using a reverse proxy (nginx)
7. Use a production ASGI server like Gunicorn

## Support

For support with Marcus Lion Transcribe, please contact the development team or create an issue in the repository.

## License

This project is proprietary software developed for Marcus Lion.
