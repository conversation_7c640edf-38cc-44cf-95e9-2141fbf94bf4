from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import List
import os
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # Database
    mongodb_url: str = os.getenv("MONGODB_URL", "")
    database_name: str = os.getenv("DATABASE_NAME", "transcriber")

    # JWT
    secret_key: str = os.getenv("SECRET_KEY", "fallback-secret-key")
    algorithm: str = os.getenv("ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

    # OAuth
    google_client_id: str = os.getenv("GOOGLE_CLIENT_ID", "")
    google_client_secret: str = os.getenv("GOOGLE_CLIENT_SECRET", "")
    github_client_id: str = os.getenv("GITHUB_CLIENT_ID", "")
    github_client_secret: str = os.getenv("GITHUB_CLIENT_SECRET", "")

    # OpenAI
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")

    # App
    app_name: str = os.getenv("APP_NAME", "Marcus Lion Transcribe")
    app_version: str = os.getenv("APP_VERSION", "1.0.0")
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"
    allowed_hosts: str = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1")

    # File Upload
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "50000000"))  # 50MB
    upload_dir: str = os.getenv("UPLOAD_DIR", "uploads/")
    allowed_extensions: str = os.getenv("ALLOWED_EXTENSIONS", "mp3,wav,m4a,flac")

    @property
    def allowed_hosts_list(self) -> List[str]:
        return self.allowed_hosts.split(",")

    @property
    def allowed_extensions_list(self) -> List[str]:
        return self.allowed_extensions.split(",")

    class Config:
        env_file = ".env"

settings = Settings()

# Create upload directory if it doesn't exist
os.makedirs(settings.upload_dir, exist_ok=True)
