from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, APIKeyHeader
from typing import Optional
from services.auth_service import auth_service
from models.user import UserInDB

# Security schemes
bearer_scheme = HTTPBearer(auto_error=False)
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)

async def get_current_user_optional(
    token: Optional[HTTPAuthorizationCredentials] = Depends(bearer_scheme),
    api_key: Optional[str] = Depends(api_key_header)
) -> Optional[UserInDB]:
    """Get current user from JWT token or API key (optional)"""
    
    # Try API key first
    if api_key:
        user = await auth_service.get_user_by_api_key(api_key)
        if user and user.is_active:
            return user
    
    # Try JWT token
    if token:
        try:
            user = await auth_service.get_current_user_from_token(token.credentials)
            if user and user.is_active:
                return user
        except HTTPException:
            pass
    
    return None

async def get_current_user(
    token: Optional[HTTPAuthorizationCredentials] = Depends(bearer_scheme),
    api_key: Optional[str] = Depends(api_key_header)
) -> UserInDB:
    """Get current user from JWT token or API key (required)"""
    
    user = await get_current_user_optional(token, api_key)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

async def get_current_active_user(current_user: UserInDB = Depends(get_current_user)) -> UserInDB:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user
