from pydantic import BaseModel, <PERSON>, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime
from bson import ObjectId
from enum import Enum

class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema: dict[str, Any]) -> dict[str, Any]:
        field_schema.update(type="string")
        return field_schema

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

class TranscriptionStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class TranscriptionBase(BaseModel):
    filename: str
    original_filename: str
    file_size: int
    duration: Optional[float] = None
    language: Optional[str] = "auto"

class TranscriptionCreate(TranscriptionBase):
    user_id: str

class TranscriptionUpdate(BaseModel):
    status: Optional[TranscriptionStatus] = None
    transcription_text: Optional[str] = None
    confidence_score: Optional[float] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None

class TranscriptionInDB(TranscriptionBase):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId
    status: TranscriptionStatus = TranscriptionStatus.PENDING
    transcription_text: Optional[str] = None
    confidence_score: Optional[float] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = {}

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

class Transcription(TranscriptionInDB):
    pass

class TranscriptionResponse(TranscriptionBase):
    id: str
    status: TranscriptionStatus
    transcription_text: Optional[str] = None
    confidence_score: Optional[float] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    created_at: datetime
    updated_at: datetime

class TranscriptionListResponse(BaseModel):
    transcriptions: list[TranscriptionResponse]
    total: int
    page: int
    per_page: int
