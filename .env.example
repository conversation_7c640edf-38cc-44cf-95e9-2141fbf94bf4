# Database
MONGODB_URL=mongodb+srv://username:<EMAIL>/transcriber?retryWrites=true&w=majority
DATABASE_NAME=transcriber

# JWT Settings
SECRET_KEY=your-super-secret-jwt-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OAuth Settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# OpenAI API (for Whisper transcription)
OPENAI_API_KEY=your-openai-api-key

# App Settings
APP_NAME=Marcus Lion Transcribe
APP_VERSION=1.0.0
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# File Upload Settings
MAX_FILE_SIZE=50000000  # 50MB in bytes
UPLOAD_DIR=uploads/
ALLOWED_EXTENSIONS=mp3,wav,m4a,flac
