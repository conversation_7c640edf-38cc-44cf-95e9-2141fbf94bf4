<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app_name or "<PERSON> Lion Transcribe" }}</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="landing-page">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <img src="/static/marcus-lion-logo.png" alt="Marcus Lion" class="logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <div class="logo-fallback">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <span>{{ app_name or "Marcus Lion Transcribe" }}</span>
                </div>
                <div class="nav-menu">
                    <a href="/app" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> Get Started
                    </a>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <h1>Professional Audio Transcription</h1>
                    <p class="hero-subtitle">Transform your audio files into accurate text with AI-powered transcription technology</p>
                    <div class="hero-features">
                        <div class="feature-item">
                            <i class="fas fa-bolt"></i>
                            <span>Fast & Accurate</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Secure & Private</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-globe"></i>
                            <span>Multi-Language</span>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <a href="/app" class="btn btn-primary btn-large">
                            <i class="fas fa-play"></i> Start Transcribing
                        </a>
                        <a href="/docs" class="btn btn-secondary btn-large">
                            <i class="fas fa-book"></i> API Documentation
                        </a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="transcription-demo">
                        <div class="global-languages">
                            <h3>Supported Languages</h3>
                            <div class="flags-container">
                                <div class="flag-item">
                                    <span class="flag">🇺🇸</span>
                                    <span class="lang">English</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇪🇸</span>
                                    <span class="lang">Spanish</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇫🇷</span>
                                    <span class="lang">French</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇩🇪</span>
                                    <span class="lang">German</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇮🇹</span>
                                    <span class="lang">Italian</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇵🇹</span>
                                    <span class="lang">Portuguese</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇷🇺</span>
                                    <span class="lang">Russian</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇯🇵</span>
                                    <span class="lang">Japanese</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇰🇷</span>
                                    <span class="lang">Korean</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇨🇳</span>
                                    <span class="lang">Chinese</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇳🇱</span>
                                    <span class="lang">Dutch</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇸🇪</span>
                                    <span class="lang">Swedish</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇳🇴</span>
                                    <span class="lang">Norwegian</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇩🇰</span>
                                    <span class="lang">Danish</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇫🇮</span>
                                    <span class="lang">Finnish</span>
                                </div>
                                <div class="flag-item">
                                    <span class="flag">🇵🇱</span>
                                    <span class="lang">Polish</span>
                                </div>
                            </div>
                            <p class="auto-detect">+ Auto-detection for 50+ languages</p>
                        </div>
                        <div class="audio-wave">
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features">
            <div class="features-container">
                <h2>Why Choose Marcus Lion Transcribe?</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <h3>Easy Upload</h3>
                        <p>Drag and drop your audio files or browse to upload. Supports MP3, WAV, M4A, and FLAC formats.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3>AI-Powered</h3>
                        <p>Powered by OpenAI's Whisper technology for industry-leading transcription accuracy.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h3>Multi-Language</h3>
                        <p>Supports multiple languages with automatic language detection for global accessibility.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3>API Access</h3>
                        <p>Integrate transcription into your applications with our comprehensive REST API.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3>Real-Time Status</h3>
                        <p>Track your transcription progress in real-time with live status updates.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h3>Export Options</h3>
                        <p>Copy, download, or integrate your transcriptions with various export formats.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="how-it-works">
            <div class="how-it-works-container">
                <h2>How It Works</h2>
                <div class="workflow-demo">
                    <div class="demo-visual">
                        <div class="newscaster-scene">
                            <div class="news-studio">
                                <div class="newscaster">
                                    <div class="person">
                                        <div class="head">
                                            <div class="face">
                                                <div class="eyes">
                                                    <div class="eye"></div>
                                                    <div class="eye"></div>
                                                </div>
                                                <div class="mouth speaking"></div>
                                            </div>
                                            <div class="hair"></div>
                                        </div>
                                        <div class="body">
                                            <div class="suit"></div>
                                            <div class="tie"></div>
                                        </div>
                                    </div>
                                    <div class="microphone">
                                        <div class="mic-head"></div>
                                        <div class="mic-stand"></div>
                                    </div>
                                </div>
                                <div class="news-desk"></div>
                                <div class="background-screen">
                                    <div class="news-logo">📺 LIVE</div>
                                </div>
                            </div>
                            <div class="speech-bubble">
                                <p>"Breaking news: AI transcription technology revolutionizes media industry..."</p>
                                <div class="sound-waves">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>
                        </div>
                        <div class="arrow-down">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="transcription-output">
                            <div class="document">
                                <div class="doc-header">
                                    <i class="fas fa-file-text"></i>
                                    <span>Transcription.txt</span>
                                </div>
                                <div class="doc-content">
                                    <div class="text-line typing">Breaking news: AI transcription technology</div>
                                    <div class="text-line typing">revolutionizes media industry with</div>
                                    <div class="text-line typing">unprecedented accuracy and speed...</div>
                                    <div class="cursor">|</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h3>Upload Your Audio</h3>
                                <p>News broadcasts, interviews, podcasts, meetings - any audio content</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h3>AI Processing</h3>
                                <p>Advanced speech recognition analyzes tone, pace, and context</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h3>Get Your Text</h3>
                                <p>Professional-quality transcripts ready for editing and publishing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Status Section (if database not connected) -->
        {% if database_status == "Not Connected" %}
        <section class="status-section">
            <div class="status-container">
                <div class="status-card">
                    <h2><i class="fas fa-cog"></i> Development Mode</h2>
                    <p>This instance is running in development mode. To enable full functionality:</p>
                    <ul class="status-list">
                        <li><i class="fas fa-database"></i> Configure MongoDB Atlas connection</li>
                        <li><i class="fas fa-robot"></i> Add OpenAI API key for transcription</li>
                        <li><i class="fas fa-image"></i> Add Marcus Lion logo (optional)</li>
                    </ul>
                    <p><strong>You can still test the interface!</strong> Authentication works in development mode.</p>
                </div>
            </div>
        </section>
        {% endif %}

        <!-- CTA Section -->
        <section class="cta">
            <div class="cta-container">
                <h2>Ready to Get Started?</h2>
                <p>Join thousands of professionals who trust Marcus Lion Transcribe for their audio transcription needs.</p>
                <a href="/app" class="btn btn-primary btn-large">
                    <i class="fas fa-rocket"></i> Start Transcribing Now
                </a>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-brand">
                        <div class="nav-brand">
                            <img src="/static/marcus-lion-logo.png" alt="Marcus Lion" class="logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                            <div class="logo-fallback">
                                <i class="fas fa-microphone"></i>
                            </div>
                            <span>{{ app_name or "Marcus Lion Transcribe" }}</span>
                        </div>
                        <p>Professional audio transcription powered by AI</p>
                    </div>
                    <div class="footer-links">
                        <div class="footer-section">
                            <h4>Product</h4>
                            <ul>
                                <li><a href="/app">Web App</a></li>
                                <li><a href="/docs">API Documentation</a></li>
                                <li><a href="/health">System Status</a></li>
                            </ul>
                        </div>
                        <div class="footer-section">
                            <h4>Features</h4>
                            <ul>
                                <li>Audio Upload</li>
                                <li>AI Transcription</li>
                                <li>Multi-Language</li>
                                <li>API Access</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2024 Marcus Lion. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Simple animation for audio wave
        document.addEventListener('DOMContentLoaded', function() {
            const waveBars = document.querySelectorAll('.wave-bar');
            waveBars.forEach((bar, index) => {
                bar.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
