<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Transcribe</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <img src="/static/marcus-lion-logo.png" alt="Marcus Lion" class="logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <div class="logo-fallback">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <span>{{ app_name or "Marcus Lion Transcribe" }}</span>
                </div>
                <div class="nav-menu">
                    <div id="auth-section">
                        <button id="login-btn" class="btn btn-primary">Login</button>
                        <button id="register-btn" class="btn btn-secondary">Register</button>
                    </div>
                    <div id="user-section" style="display: none;">
                        <span id="user-email"></span>
                        <button id="logout-btn" class="btn btn-secondary">Logout</button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Setup Status -->
            {% if database_status == "Not Connected" %}
            <div class="setup-status">
                <div class="status-card">
                    <h2><i class="fas fa-cog"></i> Setup Required</h2>
                    <p>Welcome to {{ app_name or "Marcus Lion Transcribe" }}! To get started, you need to configure:</p>
                    <ul class="setup-list">
                        <li>
                            <i class="fas fa-database"></i>
                            <strong>MongoDB Atlas:</strong>
                            <span class="status-badge status-{{ 'connected' if database_status == 'Connected' else 'disconnected' }}">
                                {{ database_status }}
                            </span>
                            <p>Add your MongoDB Atlas connection string to the <code>MONGODB_URL</code> in your <code>.env</code> file.</p>
                        </li>
                        <li>
                            <i class="fas fa-robot"></i>
                            <strong>OpenAI API:</strong>
                            <span class="status-badge status-pending">Required for transcription</span>
                            <p>Add your OpenAI API key to <code>OPENAI_API_KEY</code> in your <code>.env</code> file.</p>
                        </li>
                        <li>
                            <i class="fas fa-image"></i>
                            <strong>Marcus Lion Logo:</strong>
                            <span class="status-badge status-optional">Optional</span>
                            <p>Place your logo as <code>static/marcus-lion-logo.png</code> to replace the microphone icon.</p>
                        </li>
                    </ul>
                    <div class="setup-actions">
                        <a href="/docs" class="btn btn-primary">
                            <i class="fas fa-book"></i> View API Documentation
                        </a>
                        <button onclick="window.location.reload()" class="btn btn-secondary">
                            <i class="fas fa-refresh"></i> Refresh Status
                        </button>
                    </div>
                    <div class="dev-mode-notice">
                        <h3><i class="fas fa-code"></i> Development Mode</h3>
                        <p>You can still test the interface! Registration and login work in development mode:</p>
                        <ul>
                            <li><strong>Register:</strong> Create an account with any email/password</li>
                            <li><strong>Login:</strong> Use any email/password combination</li>
                            <li><strong>Quick Test:</strong> Email: <code><EMAIL></code>, Password: <code>password</code></li>
                        </ul>
                        <p>All authentication features work normally, but data isn't persisted without a database.</p>
                    </div>
                </div>
            </div>
            {% endif %}
            <!-- Login/Register Forms -->
            <div id="auth-forms" class="auth-container">
                <!-- Login Form -->
                <div id="login-form" class="auth-form">
                    <h2>Login</h2>
                    <form id="login-form-element">
                        <div class="form-group">
                            <label for="login-email">Email:</label>
                            <input type="email" id="login-email" required placeholder="Enter your email">
                        </div>
                        <div class="form-group">
                            <label for="login-password">Password:</label>
                            <input type="password" id="login-password" required placeholder="Enter your password">
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">Login</button>
                    </form>

                    <div class="oauth-section">
                        <p>Or login with:</p>
                        <div class="oauth-buttons">
                            <a href="/auth/google" class="btn btn-google">
                                <i class="fab fa-google"></i> Google
                            </a>
                            <a href="/auth/github" class="btn btn-github">
                                <i class="fab fa-github"></i> GitHub
                            </a>
                        </div>
                    </div>

                    <p class="auth-switch">
                        Don't have an account?
                        <a href="#" id="show-register">Register here</a>
                    </p>
                </div>

                <!-- Register Form -->
                <div id="register-form" class="auth-form" style="display: none;">
                    <h2>Register</h2>
                    <form id="register-form-element">
                        <div class="form-group">
                            <label for="register-email">Email:</label>
                            <input type="email" id="register-email" required placeholder="Enter your email">
                        </div>
                        <div class="form-group">
                            <label for="register-name">Full Name (optional):</label>
                            <input type="text" id="register-name" placeholder="Enter your full name">
                        </div>
                        <div class="form-group">
                            <label for="register-password">Password:</label>
                            <input type="password" id="register-password" required placeholder="Create a password" minlength="6">
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">Register</button>
                    </form>

                    <p class="auth-switch">
                        Already have an account?
                        <a href="#" id="show-login">Login here</a>
                    </p>
                </div>
            </div>

            <!-- Dashboard -->
            <div id="dashboard" style="display: none;">
                <!-- Upload Section -->
                <div class="upload-section">
                    <h2>Upload Audio File</h2>
                    <div class="upload-container">
                        <div id="drop-zone" class="drop-zone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag and drop your audio file here or click to browse</p>
                            <input type="file" id="file-input" accept=".mp3,.wav,.m4a,.flac" hidden>
                            <button id="browse-btn" class="btn btn-primary">Browse Files</button>
                        </div>

                        <div class="upload-options">
                            <div class="form-group">
                                <label for="language-select">Language (optional):</label>
                                <select id="language-select">
                                    <option value="auto">Auto-detect</option>
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                    <option value="pt">Portuguese</option>
                                    <option value="ru">Russian</option>
                                    <option value="ja">Japanese</option>
                                    <option value="ko">Korean</option>
                                    <option value="zh">Chinese</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Section -->
                <div id="upload-progress" class="progress-section" style="display: none;">
                    <h3>Processing...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p id="progress-text">Uploading file...</p>
                </div>

                <!-- Transcriptions List -->
                <div class="transcriptions-section">
                    <h2>Your Transcriptions</h2>
                    <div id="transcriptions-list" class="transcriptions-list">
                        <!-- Transcriptions will be loaded here -->
                    </div>
                    <div class="pagination">
                        <button id="prev-page" class="btn btn-secondary" disabled>Previous</button>
                        <span id="page-info">Page 1</span>
                        <button id="next-page" class="btn btn-secondary">Next</button>
                    </div>
                </div>

                <!-- API Key Section -->
                <div class="api-section">
                    <h2>API Access</h2>
                    <div class="api-key-container">
                        <div class="form-group">
                            <label>Your API Key:</label>
                            <div class="api-key-display">
                                <input type="password" id="api-key-display" readonly>
                                <button id="toggle-api-key" class="btn btn-secondary">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button id="copy-api-key" class="btn btn-secondary">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <button id="generate-api-key" class="btn btn-primary">Generate New API Key</button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Modal for transcription details -->
        <div id="transcription-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Transcription Details</h2>
                <div id="modal-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Toast notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
