import os
import time
import aiofiles
from typing import Optional, List
from fastapi import UploadFile, HTTPException, status
from openai import Async<PERSON>penAI
from models.transcription import (
    Transcription, TranscriptionCreate, TranscriptionUpdate,
    TranscriptionStatus, TranscriptionInDB
)
from database.connection import get_database
from config import settings
from bson import ObjectId
import uuid

class TranscriptionService:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key) if settings.openai_api_key else None

    async def save_uploaded_file(self, file: UploadFile) -> tuple[str, str]:
        """Save uploaded file and return filename and path"""
        # Generate unique filename
        file_extension = file.filename.split('.')[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        file_path = os.path.join(settings.upload_dir, unique_filename)

        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)

        return unique_filename, file_path

    async def validate_file(self, file: UploadFile) -> bool:
        """Validate uploaded file"""
        # Check file extension
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No filename provided"
            )

        file_extension = file.filename.split('.')[-1].lower()
        if file_extension not in settings.allowed_extensions_list:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed types: {', '.join(settings.allowed_extensions_list)}"
            )

        # Check file size
        file.file.seek(0, 2)  # Seek to end
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning

        if file_size > settings.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size: {settings.max_file_size / 1024 / 1024:.1f}MB"
            )

        return True

    async def create_transcription_job(self, file: UploadFile, user_id: str, language: str = "auto") -> TranscriptionInDB:
        """Create a new transcription job"""
        await self.validate_file(file)

        # Save file
        filename, file_path = await self.save_uploaded_file(file)

        # Get file size
        file_size = os.path.getsize(file_path)

        # Create transcription record
        transcription_data = TranscriptionCreate(
            filename=filename,
            original_filename=file.filename,
            file_size=file_size,
            user_id=user_id,
            language=language
        )

        db = await get_database()
        transcription_dict = transcription_data.dict()
        transcription_dict["user_id"] = ObjectId(user_id)
        transcription_dict["created_at"] = transcription_dict["updated_at"] = time.time()

        result = await db.transcriptions.insert_one(transcription_dict)
        transcription_dict["_id"] = result.inserted_id

        return TranscriptionInDB(**transcription_dict)

    async def process_transcription(self, transcription_id: str) -> TranscriptionInDB:
        """Process transcription using OpenAI Whisper"""
        if not self.client:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OpenAI API key not configured"
            )

        db = await get_database()
        transcription = await db.transcriptions.find_one({"_id": ObjectId(transcription_id)})

        if not transcription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transcription not found"
            )

        # Update status to processing
        await db.transcriptions.update_one(
            {"_id": ObjectId(transcription_id)},
            {"$set": {"status": TranscriptionStatus.PROCESSING, "updated_at": time.time()}}
        )

        try:
            start_time = time.time()
            file_path = os.path.join(settings.upload_dir, transcription["filename"])

            # Transcribe with OpenAI Whisper
            with open(file_path, "rb") as audio_file:
                transcript = await self.client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    language=transcription.get("language") if transcription.get("language") != "auto" else None
                )

            processing_time = time.time() - start_time

            # Update transcription with results
            update_data = {
                "status": TranscriptionStatus.COMPLETED,
                "transcription_text": transcript.text,
                "processing_time": processing_time,
                "updated_at": time.time()
            }

            await db.transcriptions.update_one(
                {"_id": ObjectId(transcription_id)},
                {"$set": update_data}
            )

            # Update user transcription count
            await db.users.update_one(
                {"_id": ObjectId(transcription["user_id"])},
                {"$inc": {"transcription_count": 1}}
            )

        except Exception as e:
            # Update transcription with error
            await db.transcriptions.update_one(
                {"_id": ObjectId(transcription_id)},
                {"$set": {
                    "status": TranscriptionStatus.FAILED,
                    "error_message": str(e),
                    "updated_at": time.time()
                }}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Transcription failed: {str(e)}"
            )

        # Return updated transcription
        updated_transcription = await db.transcriptions.find_one({"_id": ObjectId(transcription_id)})
        return TranscriptionInDB(**updated_transcription)

    async def get_transcription(self, transcription_id: str, user_id: str) -> Optional[TranscriptionInDB]:
        """Get transcription by ID for a specific user"""
        db = await get_database()
        transcription = await db.transcriptions.find_one({
            "_id": ObjectId(transcription_id),
            "user_id": ObjectId(user_id)
        })

        if transcription:
            return TranscriptionInDB(**transcription)
        return None

    async def get_user_transcriptions(self, user_id: str, skip: int = 0, limit: int = 10) -> List[TranscriptionInDB]:
        """Get transcriptions for a user with pagination"""
        db = await get_database()
        cursor = db.transcriptions.find({"user_id": ObjectId(user_id)}).sort("created_at", -1).skip(skip).limit(limit)
        transcriptions = []

        async for transcription in cursor:
            transcriptions.append(TranscriptionInDB(**transcription))

        return transcriptions

    async def delete_transcription(self, transcription_id: str, user_id: str) -> bool:
        """Delete transcription and associated file"""
        db = await get_database()
        transcription = await db.transcriptions.find_one({
            "_id": ObjectId(transcription_id),
            "user_id": ObjectId(user_id)
        })

        if not transcription:
            return False

        # Delete file
        file_path = os.path.join(settings.upload_dir, transcription["filename"])
        if os.path.exists(file_path):
            os.remove(file_path)

        # Delete database record
        result = await db.transcriptions.delete_one({"_id": ObjectId(transcription_id)})
        return result.deleted_count > 0

transcription_service = TranscriptionService()
