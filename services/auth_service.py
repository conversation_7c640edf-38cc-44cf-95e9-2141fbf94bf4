from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import <PERSON><PERSON><PERSON>ontext
from fastapi import HTTPEx<PERSON>, status
from models.user import User, UserCreate, UserInDB
from database.connection import get_database
from config import settings
import secrets
import string
from bson import ObjectId

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    def __init__(self):
        self.pwd_context = pwd_context

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return self.pwd_context.hash(password)

    def generate_api_key(self) -> str:
        """Generate a secure API key"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(32))

    async def get_user_by_email(self, email: str) -> Optional[UserInDB]:
        """Get user by email"""
        db = await get_database()
        user_data = await db.users.find_one({"email": email})
        if user_data:
            return UserInDB(**user_data)
        return None

    async def get_user_by_id(self, user_id: str) -> Optional[UserInDB]:
        """Get user by ID"""
        db = await get_database()
        user_data = await db.users.find_one({"_id": ObjectId(user_id)})
        if user_data:
            return UserInDB(**user_data)
        return None

    async def get_user_by_api_key(self, api_key: str) -> Optional[UserInDB]:
        """Get user by API key"""
        db = await get_database()
        user_data = await db.users.find_one({"api_key": api_key})
        if user_data:
            return UserInDB(**user_data)
        return None

    async def create_user(self, user: UserCreate) -> UserInDB:
        """Create a new user"""
        db = await get_database()

        # Check if user already exists
        existing_user = await self.get_user_by_email(user.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        user_dict = user.dict()

        # Hash password if provided
        if user.password:
            user_dict["hashed_password"] = self.get_password_hash(user.password)
            del user_dict["password"]

        # Generate API key
        user_dict["api_key"] = self.generate_api_key()
        user_dict["created_at"] = datetime.utcnow()
        user_dict["updated_at"] = datetime.utcnow()

        result = await db.users.insert_one(user_dict)
        user_dict["_id"] = result.inserted_id

        return UserInDB(**user_dict)

    async def authenticate_user(self, email: str, password: str) -> Optional[UserInDB]:
        """Authenticate user with email and password"""
        user = await self.get_user_by_email(email)
        if not user or not user.hashed_password:
            return None
        if not self.verify_password(password, user.hashed_password):
            return None
        return user

    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """Create JWT access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt

    async def get_current_user_from_token(self, token: str) -> UserInDB:
        """Get current user from JWT token"""
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            email: str = payload.get("sub")
            if email is None:
                raise credentials_exception
        except JWTError:
            raise credentials_exception

        try:
            user = await self.get_user_by_email(email)
            if user is None:
                raise credentials_exception
            return user
        except HTTPException as e:
            if e.status_code == 503:  # Database not available - return dev user
                from datetime import datetime
                from bson import ObjectId
                return UserInDB(
                    id=ObjectId(),
                    email=email,
                    full_name="Development User",
                    is_active=True,
                    api_key="dev-api-key-123",
                    oauth_provider=None,
                    oauth_id=None,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    transcription_count=0
                )
            raise

auth_service = AuthService()
