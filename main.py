from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import Static<PERSON>iles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import uvicorn
import logging

from database.connection import connect_to_mongo, close_mongo_connection
from routers import auth, transcription
from config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Marcus Lion Transcribe - Professional audio transcription service with authentication and MongoDB storage"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Include routers
app.include_router(auth.router)
app.include_router(transcription.router)

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize database connection on startup"""
    await connect_to_mongo()
    logger.info("Application started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Close database connection on shutdown"""
    await close_mongo_connection()
    logger.info("Application shutdown complete")

# Root endpoint - serve the landing page
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serve the landing page"""
    from database.connection import db
    database_status = "Connected" if db.database else "Not Connected"
    return templates.TemplateResponse("landing.html", {
        "request": request,
        "database_status": database_status,
        "app_name": settings.app_name
    })

# App endpoint - serve the main application UI (requires login)
@app.get("/app", response_class=HTMLResponse)
async def read_app(request: Request):
    """Serve the main application UI"""
    from database.connection import db
    database_status = "Connected" if db.database else "Not Connected"
    return templates.TemplateResponse("app.html", {
        "request": request,
        "database_status": database_status,
        "app_name": settings.app_name
    })

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": settings.app_version}

# API info endpoint
@app.get("/api/info")
async def api_info():
    """API information endpoint"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": "Marcus Lion Transcribe - Professional audio transcription service",
        "endpoints": {
            "auth": "/auth",
            "transcriptions": "/transcriptions",
            "docs": "/docs",
            "health": "/health"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8200,
        reload=settings.debug,
        log_level="info"
    )
