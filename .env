# Database - Add your MongoDB Atlas connection string here
MONGODB_URL=mongodb://localhost:27017/transcriber
DATABASE_NAME=transcriber

# JWT Settings
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OAuth Settings (optional - leave empty if not using)
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# OpenAI API (required for transcription - add your key here)
OPENAI_API_KEY=

# App Settings
APP_NAME=Marcus Lion Transcribe
APP_VERSION=1.0.0
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# File Upload Settings
MAX_FILE_SIZE=50000000
UPLOAD_DIR=uploads/
ALLOWED_EXTENSIONS=mp3,wav,m4a,flac
