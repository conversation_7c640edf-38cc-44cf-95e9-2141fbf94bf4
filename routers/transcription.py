from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, BackgroundTasks, Query
from typing import List, Optional
from models.transcription import TranscriptionResponse, TranscriptionListResponse
from services.transcription_service import transcription_service
from middleware.auth import get_current_active_user
from models.user import UserInDB

router = APIRouter(prefix="/transcriptions", tags=["transcriptions"])

@router.post("/upload", response_model=TranscriptionResponse)
async def upload_audio_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = "auto",
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Upload an audio file for transcription"""

    try:
        # Create transcription job
        transcription = await transcription_service.create_transcription_job(
            file=file,
            user_id=str(current_user.id),
            language=language
        )

        # Add transcription processing to background tasks
        background_tasks.add_task(
            transcription_service.process_transcription,
            str(transcription.id)
        )

        return TranscriptionResponse(
            id=str(transcription.id),
            filename=transcription.filename,
            original_filename=transcription.original_filename,
            file_size=transcription.file_size,
            duration=transcription.duration,
            language=transcription.language,
            status=transcription.status,
            transcription_text=transcription.transcription_text,
            confidence_score=transcription.confidence_score,
            error_message=transcription.error_message,
            processing_time=transcription.processing_time,
            created_at=transcription.created_at,
            updated_at=transcription.updated_at
        )
    except HTTPException as e:
        if e.status_code == 503:  # Database not available
            raise HTTPException(
                status_code=503,
                detail="File upload requires database connection. Please configure MongoDB Atlas connection in .env file."
            )
        raise

@router.get("/", response_model=TranscriptionListResponse)
async def get_transcriptions(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get user's transcriptions with pagination"""

    transcriptions = await transcription_service.get_user_transcriptions(
        user_id=str(current_user.id),
        skip=skip,
        limit=limit
    )

    # Convert to response models
    transcription_responses = []
    for t in transcriptions:
        transcription_responses.append(TranscriptionResponse(
            id=str(t.id),
            filename=t.filename,
            original_filename=t.original_filename,
            file_size=t.file_size,
            duration=t.duration,
            language=t.language,
            status=t.status,
            transcription_text=t.transcription_text,
            confidence_score=t.confidence_score,
            error_message=t.error_message,
            processing_time=t.processing_time,
            created_at=t.created_at,
            updated_at=t.updated_at
        ))

    return TranscriptionListResponse(
        transcriptions=transcription_responses,
        total=len(transcription_responses),
        page=skip // limit + 1,
        per_page=limit
    )

@router.get("/{transcription_id}", response_model=TranscriptionResponse)
async def get_transcription(
    transcription_id: str,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get a specific transcription"""

    transcription = await transcription_service.get_transcription(
        transcription_id=transcription_id,
        user_id=str(current_user.id)
    )

    if not transcription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transcription not found"
        )

    return TranscriptionResponse(
        id=str(transcription.id),
        filename=transcription.filename,
        original_filename=transcription.original_filename,
        file_size=transcription.file_size,
        duration=transcription.duration,
        language=transcription.language,
        status=transcription.status,
        transcription_text=transcription.transcription_text,
        confidence_score=transcription.confidence_score,
        error_message=transcription.error_message,
        processing_time=transcription.processing_time,
        created_at=transcription.created_at,
        updated_at=transcription.updated_at
    )

@router.delete("/{transcription_id}")
async def delete_transcription(
    transcription_id: str,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Delete a transcription"""

    success = await transcription_service.delete_transcription(
        transcription_id=transcription_id,
        user_id=str(current_user.id)
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transcription not found"
        )

    return {"message": "Transcription deleted successfully"}

@router.post("/{transcription_id}/reprocess", response_model=TranscriptionResponse)
async def reprocess_transcription(
    transcription_id: str,
    background_tasks: BackgroundTasks,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Reprocess a failed transcription"""

    transcription = await transcription_service.get_transcription(
        transcription_id=transcription_id,
        user_id=str(current_user.id)
    )

    if not transcription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transcription not found"
        )

    # Add transcription processing to background tasks
    background_tasks.add_task(
        transcription_service.process_transcription,
        transcription_id
    )

    return TranscriptionResponse(
        id=str(transcription.id),
        filename=transcription.filename,
        original_filename=transcription.original_filename,
        file_size=transcription.file_size,
        duration=transcription.duration,
        language=transcription.language,
        status=transcription.status,
        transcription_text=transcription.transcription_text,
        confidence_score=transcription.confidence_score,
        error_message=transcription.error_message,
        processing_time=transcription.processing_time,
        created_at=transcription.created_at,
        updated_at=transcription.updated_at
    )
