from fastapi import API<PERSON>outer, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse
from authlib.integrations.starlette_client import OAuth
from datetime import timed<PERSON><PERSON>
from typing import Optional

from models.user import User<PERSON><PERSON>, UserResponse, Token, APIKeyResponse
from services.auth_service import auth_service
from middleware.auth import get_current_active_user
from config import settings

router = APIRouter(prefix="/auth", tags=["authentication"])

# OAuth setup
oauth = OAuth()
oauth.register(
    name='google',
    client_id=settings.google_client_id,
    client_secret=settings.google_client_secret,
    server_metadata_url='https://accounts.google.com/.well-known/openid_configuration',
    client_kwargs={
        'scope': 'openid email profile'
    }
)

oauth.register(
    name='github',
    client_id=settings.github_client_id,
    client_secret=settings.github_client_secret,
    access_token_url='https://github.com/login/oauth/access_token',
    authorize_url='https://github.com/login/oauth/authorize',
    api_base_url='https://api.github.com/',
    client_kwargs={'scope': 'user:email'},
)

@router.post("/register", response_model=UserResponse)
async def register(user: UserCreate):
    """Register a new user"""
    try:
        db_user = await auth_service.create_user(user)
        return UserResponse(
            id=str(db_user.id),
            email=db_user.email,
            full_name=db_user.full_name,
            is_active=db_user.is_active,
            api_key=db_user.api_key,
            oauth_provider=db_user.oauth_provider,
            created_at=db_user.created_at,
            transcription_count=db_user.transcription_count
        )
    except HTTPException as e:
        if e.status_code == 503:  # Database not available - use dev mode
            from datetime import datetime
            return UserResponse(
                id="dev-user-123",
                email=user.email,
                full_name=user.full_name or "Development User",
                is_active=True,
                api_key="dev-api-key-123",
                oauth_provider=None,
                created_at=datetime.utcnow(),
                transcription_count=0
            )
        raise

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """Login with email and password"""
    try:
        user = await auth_service.authenticate_user(form_data.username, form_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = auth_service.create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException as e:
        if e.status_code == 503:  # Database not available - use dev mode
            # In development mode, accept any login
            access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
            access_token = auth_service.create_access_token(
                data={"sub": form_data.username}, expires_delta=access_token_expires
            )
            return {"access_token": access_token, "token_type": "bearer"}
        raise

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: UserResponse = Depends(get_current_active_user)):
    """Get current user information"""
    return UserResponse(
        id=str(current_user.id),
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        api_key=current_user.api_key,
        oauth_provider=current_user.oauth_provider,
        created_at=current_user.created_at,
        transcription_count=current_user.transcription_count
    )

@router.post("/api-key", response_model=APIKeyResponse)
async def generate_api_key(current_user = Depends(get_current_active_user)):
    """Generate a new API key for the current user"""
    new_api_key = auth_service.generate_api_key()

    # Update user with new API key
    from database.connection import get_database
    from bson import ObjectId

    db = await get_database()
    await db.users.update_one(
        {"_id": ObjectId(current_user.id)},
        {"$set": {"api_key": new_api_key}}
    )

    return APIKeyResponse(
        api_key=new_api_key,
        name="Default API Key",
        created_at=current_user.created_at
    )

# OAuth routes
@router.get("/google")
async def google_login(request: Request):
    """Initiate Google OAuth login"""
    redirect_uri = request.url_for('google_callback')
    return await oauth.google.authorize_redirect(request, redirect_uri)

@router.get("/google/callback")
async def google_callback(request: Request):
    """Handle Google OAuth callback"""
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = token.get('userinfo')

        if user_info:
            # Check if user exists
            user = await auth_service.get_user_by_email(user_info['email'])

            if not user:
                # Create new user
                user_create = UserCreate(
                    email=user_info['email'],
                    full_name=user_info.get('name'),
                    oauth_provider='google',
                    oauth_id=user_info['sub']
                )
                user = await auth_service.create_user(user_create)

            # Create access token
            access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
            access_token = auth_service.create_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )

            # Redirect to frontend with token
            return RedirectResponse(url=f"/?token={access_token}")

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/github")
async def github_login(request: Request):
    """Initiate GitHub OAuth login"""
    redirect_uri = request.url_for('github_callback')
    return await oauth.github.authorize_redirect(request, redirect_uri)

@router.get("/github/callback")
async def github_callback(request: Request):
    """Handle GitHub OAuth callback"""
    try:
        token = await oauth.github.authorize_access_token(request)

        # Get user info from GitHub API
        resp = await oauth.github.get('user', token=token)
        user_info = resp.json()

        # Get user email
        email_resp = await oauth.github.get('user/emails', token=token)
        emails = email_resp.json()
        primary_email = next((email['email'] for email in emails if email['primary']), None)

        if user_info and primary_email:
            # Check if user exists
            user = await auth_service.get_user_by_email(primary_email)

            if not user:
                # Create new user
                user_create = UserCreate(
                    email=primary_email,
                    full_name=user_info.get('name'),
                    oauth_provider='github',
                    oauth_id=str(user_info['id'])
                )
                user = await auth_service.create_user(user_create)

            # Create access token
            access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
            access_token = auth_service.create_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )

            # Redirect to frontend with token
            return RedirectResponse(url=f"/?token={access_token}")

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
